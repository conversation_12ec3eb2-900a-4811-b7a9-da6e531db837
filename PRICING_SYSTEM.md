# 💰 Pricing & Bargaining System

The Glyphic Sales Agent now includes a sophisticated pricing validation and bargaining system that ensures profitable deals while allowing strategic negotiations.

## 🎯 Key Features

### ✅ **Enforced Minimum Pricing**
- **Absolute minimum**: $120/seat/month (never goes below this)
- **Standard price**: $140/seat/month
- **Built-in validation**: Contract tool automatically rejects pricing below minimum

### 🤝 **Strategic Bargaining**
- Agents don't immediately reveal pricing
- Build value first, then discuss discounts
- Volume-based discount structure
- Professional negotiation flow

### 🛡️ **Automatic Validation**
- Real-time price validation during contract signing
- Clear error messages for invalid pricing
- Automatic calculation of discounts and savings

## 📊 Pricing Structure

### Standard Pricing
- **Base Rate**: $140/seat/month
- **Minimum Rate**: $120/seat/month (14.3% max discount)

### Volume Discounts
- **10+ seats**: Up to $130/seat/month (7.1% discount)
- **25+ seats**: Up to $125/seat/month (10.7% discount)  
- **50+ seats**: Up to $120/seat/month (14.3% discount)

### Contract Length
- **Minimum**: 1 month
- **Standard**: 12 months
- **Flexible**: Any length supported

## 🤖 Agent Behavior

### Contract Agent Instructions
The contract agent now follows a strategic approach:

1. **Collect Basic Info** first (company, contact, seats, dates, email)
2. **Build Value** before discussing price
3. **Ask about discounts**: "Are you interested in hearing about our volume discounts?"
4. **Use pricing calculator** to determine fair pricing
5. **Validate pricing** before contract execution
6. **Professional presentation** of discounts as "special pricing"

### Bargaining Flow
```
Agent: "Are you interested in hearing about our volume discounts?"
↓
Customer: "Yes, what discounts do you offer?"
↓
Agent: Uses calculate_contract_price tool
↓
Agent: "For 25 seats over 12 months, I can offer special pricing at $125/seat/month..."
↓
Contract tool validates pricing automatically
```

## 🔧 Technical Implementation

### Price Validation Function
```python
def _validate_pricing(contract_price, seats, start_date, end_date):
    # Calculates price per seat per month
    # Enforces $120 minimum
    # Returns validation result with clear error messages
```

### Contract Tool Protection
```python
@function_tool
def alguna_tool(..., contract_price: int, ...):
    # CRITICAL: Validate pricing before proceeding
    pricing_validation = _validate_pricing(...)
    
    if not pricing_validation["valid"]:
        return f"❌ CONTRACT REJECTED: {pricing_validation['error']}"
```

### Pricing Calculator Tool
```python
@function_tool
def calculate_contract_price(seats, months, price_per_seat=140):
    # Validates against minimum pricing
    # Calculates total contract value
    # Shows discount information
    # Helps agents during negotiations
```

## 📈 Business Benefits

### Revenue Protection
- **Guaranteed minimum margins** with $120/seat/month floor
- **No accidental underpricing** due to automatic validation
- **Clear discount structure** prevents excessive discounting

### Sales Efficiency
- **Guided negotiation process** for agents
- **Professional discount presentation** 
- **Automatic calculations** reduce errors
- **Value-first approach** improves close rates

### Customer Experience
- **Transparent pricing** with clear discount explanations
- **Professional negotiation** process
- **Fair volume discounts** for larger deals
- **Immediate contract validation** prevents delays

## 🚨 Error Handling

### Invalid Pricing Examples
```
❌ CONTRACT REJECTED: Price too low! Minimum price is $120/seat/month. 
   For 25 seats over 12 months, minimum total is $36,000.
```

### Successful Validation
```
✅ Contract successfully signed for Acme Corp at $125.00/seat/month! 
   Contract completion email sent successfully...
```

## 🎮 Testing Results

The system has been thoroughly tested with various scenarios:

- ✅ **Standard pricing** ($140/seat/month) - Accepted
- ✅ **Volume discounts** ($130-$120/seat/month) - Accepted with discount info
- ✅ **Minimum pricing** ($120/seat/month) - Accepted at floor price
- ❌ **Below minimum** (<$120/seat/month) - Rejected with clear error
- ✅ **Various contract lengths** - All supported with proper calculations

## 💡 Usage Tips

### For Sales Agents
1. **Build value first** before discussing price
2. **Use the pricing calculator** to explore options
3. **Frame discounts positively** as "special pricing" or "volume discounts"
4. **Let the system validate** - it will catch pricing errors

### For Managers
1. **Monitor discount patterns** to ensure profitability
2. **Adjust volume thresholds** if needed in the code
3. **Review rejected contracts** to understand pricing pressure
4. **Train agents** on the value-first approach

---

🎉 **The pricing system ensures profitable deals while enabling strategic negotiations!**

**Key Guarantee**: No contract can be signed below $120/seat/month - the system will automatically reject it.
