# File generated from our OpenAPI spec by <PERSON><PERSON><PERSON>. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import <PERSON>
from typing_extensions import TypeAlias

from .text_block_param import Text<PERSON>lockParam
from .image_block_param import Image<PERSON>lockParam
from .document_block_param import <PERSON>ume<PERSON><PERSON><PERSON><PERSON>aram
from .thinking_block_param import <PERSON><PERSON><PERSON><PERSON>ara<PERSON>
from .tool_use_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ara<PERSON>
from .tool_result_block_param import ToolResult<PERSON><PERSON>Param
from .search_result_block_param import SearchResultBlockParam
from .server_tool_use_block_param import ServerToolUseBlockParam
from .redacted_thinking_block_param import RedactedThinking<PERSON>lockParam
from .web_search_tool_result_block_param import WebSearchToolResultBlockParam

__all__ = ["ContentBlockParam"]

ContentBlockParam: TypeAlias = Union[
    TextBlockParam,
    ImageBlockParam,
    DocumentBlockParam,
    SearchResultBlockParam,
    ThinkingBlockParam,
    RedactedThinking<PERSON>lockPara<PERSON>,
    <PERSON>l<PERSON><PERSON><PERSON><PERSON>Para<PERSON>,
    ToolResultBlockParam,
    ServerToolUseBlockParam,
    WebSearchToolResultBlockParam,
]
