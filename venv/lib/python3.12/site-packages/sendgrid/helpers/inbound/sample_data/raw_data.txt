--xYzZY
Content-Disposition: form-data; name="dkim"

{@sendgrid.com : pass}
--xYzZY
Content-Disposition: form-data; name="email"

MIME-Version: 1.0
Received: by 0.0.0.0 with HTTP; Wed, 10 Aug 2016 14:44:21 -0700 (PDT)
From: Example User <<EMAIL>>
Date: Wed, 10 Aug 2016 14:44:21 -0700
Subject: Inbound Parse Test Raw Data
To: <EMAIL>
Content-Type: multipart/alternative; boundary=001a113ee97c89842f0539be8e7a

--001a113ee97c89842f0539be8e7a
Content-Type: text/plain; charset=UTF-8

Hello Twilio SendGrid!

--001a113ee97c89842f0539be8e7a
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: quoted-printable

<html><body><strong>Hello Twilio SendGrid!</body></html>

--001a113ee97c89842f0539be8e7a--

--xYzZY
Content-Disposition: form-data; name="to"

<EMAIL>
--xYzZY
Content-Disposition: form-data; name="from"

Example User <<EMAIL>>
--xYzZY
Content-Disposition: form-data; name="sender_ip"

0.0.0.0
--xYzZY
Content-Disposition: form-data; name="envelope"

{"to":["<EMAIL>"],"from":"<EMAIL>"}
--xYzZY
Content-Disposition: form-data; name="subject"

Testing with Request.bin
--xYzZY
Content-Disposition: form-data; name="charsets"

{"to":"UTF-8","subject":"UTF-8","from":"UTF-8"}
--xYzZY
Content-Disposition: form-data; name="SPF"

pass
--xYzZY--