--xYzZY
Content-Disposition: form-data; name="headers"

MIME-Version: 1.0
Received: by 0.0.0.0 with HTTP; Wed, 10 Aug 2016 18:10:13 -0700 (PDT)
From: Example User <<EMAIL>>
Date: Wed, 10 Aug 2016 18:10:13 -0700
Subject: Inbound Parse Test Data
To: <EMAIL>
Content-Type: multipart/alternative; boundary=001a113df448cad2d00539c16e89

--xYzZY
Content-Disposition: form-data; name="dkim"

{@sendgrid.com : pass}
--xYzZY
Content-Disposition: form-data; name="to"

<EMAIL>
--xYzZY
Content-Disposition: form-data; name="html"

<html><body><strong>Hello Twilio SendGrid!</body></html>

--xYzZY
Content-Disposition: form-data; name="from"

Example User <<EMAIL>>
--xYzZY
Content-Disposition: form-data; name="text"

Hello Twilio SendGrid!

--xYzZY
Content-Disposition: form-data; name="sender_ip"

0.0.0.0
--xYzZY
Content-Disposition: form-data; name="envelope"

{"to":["<EMAIL>"],"from":"<EMAIL>"}
--xYzZY
Content-Disposition: form-data; name="attachments"

0
--xYzZY
Content-Disposition: form-data; name="subject"

Testing non-raw
--xYzZY
Content-Disposition: form-data; name="charsets"

{"to":"UTF-8","html":"UTF-8","subject":"UTF-8","from":"UTF-8","text":"UTF-8"}
--xYzZY
Content-Disposition: form-data; name="SPF"

pass
--xYzZY--