# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["ChatCompletionMessageCustomToolCall", "Custom"]


class Custom(BaseModel):
    input: str
    """The input for the custom tool call generated by the model."""

    name: str
    """The name of the custom tool to call."""


class ChatCompletionMessageCustomToolCall(BaseModel):
    id: str
    """The ID of the tool call."""

    custom: Custom
    """The custom tool that the model called."""

    type: Literal["custom"]
    """The type of the tool. Always `custom`."""
