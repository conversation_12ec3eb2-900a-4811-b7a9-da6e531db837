# Email Service Setup Guide

This guide explains how to set up real email sending for the Glyphic Sales Agent using SendGrid.

## 🚀 Quick Start (Demo Mode)

The email service works out of the box in **demo mode**. Without any configuration, emails will be logged to the console with full content preview. This is perfect for development and testing.

## 📧 Production Email Setup with SendGrid

### Step 1: Create a SendGrid Account

1. Go to [SendGrid.com](https://sendgrid.com/)
2. Sign up for a free account (100 emails/day free tier)
3. Verify your email address

### Step 2: Get Your API Key

1. Log into your SendGrid dashboard
2. Go to **Settings** → **API Keys**
3. Click **Create API Key**
4. Choose **Restricted Access** and give it these permissions:
   - **Mail Send**: Full Access
5. Copy the generated API key (you won't see it again!)

### Step 3: Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp app/.env.example app/.env
   ```

2. Edit `app/.env` and add your SendGrid configuration:
   ```bash
   # OpenAI API Key (required)
   OPENAI_API_KEY=your_openai_api_key_here
   
   # SendGrid Email Service
   SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Glyphic AI Team
   ```

### Step 4: Domain Authentication (Recommended)

For better deliverability, authenticate your sending domain:

1. In SendGrid dashboard, go to **Settings** → **Sender Authentication**
2. Click **Authenticate Your Domain**
3. Follow the DNS setup instructions for your domain
4. Update `FROM_EMAIL` in your `.env` file to use your authenticated domain

## 🎨 Email Features

### Professional HTML Emails
- Beautiful responsive design
- Company branding
- Professional styling with gradients and colors
- Mobile-friendly layout

### Contract Completion Email Includes:
- ✅ Personalized greeting
- 📋 Complete contract details
- 🚀 Clear next steps
- ✨ What to expect section
- 📞 Support contact information

### Fallback Support
- HTML email with plain text fallback
- Console logging when SendGrid is not configured
- Graceful error handling

## 🔧 Testing

### Test in Demo Mode (No SendGrid needed)
```bash
source venv/bin/activate
cd app
python -c "
from email_service import email_service
result = email_service.send_contract_completion_email(
    email='<EMAIL>',
    company_name='Test Company',
    person_name='John Doe',
    number_of_recording_seats=25,
    contract_start_date='2025-09-01',
    contract_end_date='2026-08-31',
    contract_price=42000
)
print(result)
"
```

### Test with Real SendGrid
1. Set up your `.env` file with real SendGrid credentials
2. Run the same test above
3. Check your email inbox!

## 🛠 Alternative Email Services

If you prefer a different email service, you can easily modify `app/email_service.py`:

### Mailgun
```bash
pip install requests
# Modify EmailService class to use Mailgun API
```

### AWS SES
```bash
pip install boto3
# Modify EmailService class to use AWS SES
```

### SMTP (Gmail, Outlook, etc.)
```bash
pip install smtplib
# Modify EmailService class to use SMTP
```

## 🔍 Troubleshooting

### Common Issues

1. **"SendGrid API key not found"**
   - Make sure `SENDGRID_API_KEY` is set in your `.env` file
   - Restart your server after adding environment variables

2. **Emails not being delivered**
   - Check your SendGrid dashboard for delivery status
   - Verify your domain authentication
   - Check spam folders

3. **API key permissions error**
   - Ensure your API key has "Mail Send" permissions
   - Try creating a new API key with full access

### Debug Mode
Set logging level to see detailed email service logs:
```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 📊 Monitoring

Monitor your email sending in the SendGrid dashboard:
- **Activity** → View email delivery status
- **Statistics** → Track open rates, clicks, bounces
- **Suppressions** → Manage unsubscribes and bounces

## 🔐 Security Best Practices

1. **Never commit API keys** to version control
2. **Use environment variables** for all sensitive data
3. **Rotate API keys** regularly
4. **Use restricted permissions** on API keys
5. **Monitor usage** in SendGrid dashboard

## 💡 Tips

- **Free tier limits**: SendGrid free tier allows 100 emails/day
- **Rate limiting**: SendGrid has rate limits, the service handles this gracefully
- **Templates**: Consider using SendGrid templates for more complex emails
- **Analytics**: Use SendGrid's analytics to track email performance

---

🎉 **You're all set!** Your Glyphic Sales Agent now sends professional emails after contract signing.
