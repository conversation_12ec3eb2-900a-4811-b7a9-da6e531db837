# Glyphic Sales Agent - Deployment Guide

## Quick Start

1. **Set up environment variables:**
   ```bash
   cp app/.env.example app/.env
   # Edit app/.env with your API keys
   ```

2. **Deploy with <PERSON>er Compose (Recommended):**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **Access the app:**
   - Open http://localhost:8080
   - Click "Connect" to start a conversation

## Alternative Deployment Methods

### Docker Only
```bash
cd app
docker build -t glyphic-sales-agent .
docker run -p 8080:8080 --env-file .env glyphic-sales-agent
```

### Local Development
```bash
cd app
pip install -r requirements.txt
export OPENAI_API_KEY="your_key"
python server.py
```

## Environment Variables

- `OPENAI_API_KEY` - Required for AI functionality
- `ANTHROPIC_API_KEY` - Required for Claude models
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8080)

## Troubleshooting

- If agents package is missing, you may need to install it separately
- Ensure all API keys are properly set in .env file
- Check Docker logs: `docker-compose logs -f`

