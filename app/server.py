import asyncio
import base64
import json
import logging
import struct
import os
from contextlib import asynccontextmanager
from typing import TYPE_CHECKING, Any

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from typing_extensions import assert_never

print("DEBUG: Basic imports successful")

# Ensure OpenAI API key is set
if not os.getenv("OPENAI_API_KEY"):
    raise ValueError("OPENAI_API_KEY environment variable is required")

print("DEBUG: OpenAI API key check passed")

try:
    from agents.realtime import RealtimeRunner, RealtimeSession, RealtimeSessionEvent
    print("DEBUG: agents.realtime import successful")
except ImportError as e:
    print(f"DEBUG: agents.realtime import FAILED: {e}")
    raise

# Import TwilioHandler class - handle both module and package use cases
if TYPE_CHECKING:
    # For type checking, use the relative import
    from .agent import get_starting_agent
else:
    # At runtime, try both import styles
    try:
        # Try relative import first (when used as a package)
        from .agent import get_starting_agent
        print("DEBUG: agent import successful (relative)")
    except ImportError as e1:
        print(f"DEBUG: relative agent import failed: {e1}")
        try:
            # Fall back to direct import (when run as a script)
            from agent import get_starting_agent
            print("DEBUG: agent import successful (direct)")
        except ImportError as e2:
            print(f"DEBUG: direct agent import FAILED: {e2}")
            raise


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealtimeWebSocketManager:
    def __init__(self):
        self.active_sessions: dict[str, RealtimeSession] = {}
        self.session_contexts: dict[str, Any] = {}
        self.websockets: dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        print(f"DEBUG: Starting WebSocket manager connect for {session_id}")
        await websocket.accept()
        print(f"DEBUG: WebSocket accepted for {session_id}")
        self.websockets[session_id] = websocket

        try:
            print(f"DEBUG: Getting starting agent for {session_id}")
            agent = get_starting_agent()
            print(f"DEBUG: Agent created: {agent}")
            
            print(f"DEBUG: Creating RealtimeRunner for {session_id}")
            runner = RealtimeRunner(agent)
            print(f"DEBUG: RealtimeRunner created for {session_id}")
            
            print(f"DEBUG: Starting runner.run() for {session_id}")
            session_context = await runner.run()
            print(f"DEBUG: runner.run() completed for {session_id}")
            
            session = await session_context.__aenter__()
            print(f"DEBUG: Session context entered for {session_id}")
            
            self.active_sessions[session_id] = session
            self.session_contexts[session_id] = session_context

            # Start event processing task
            asyncio.create_task(self._process_events(session_id))
            print(f"DEBUG: Event processing task started for {session_id}")
        except Exception as e:
            print(f"DEBUG: ERROR in WebSocket manager connect: {e}")
            raise

    async def disconnect(self, session_id: str):
        if session_id in self.session_contexts:
            await self.session_contexts[session_id].__aexit__(None, None, None)
            del self.session_contexts[session_id]
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
        if session_id in self.websockets:
            del self.websockets[session_id]

    async def send_audio(self, session_id: str, audio_bytes: bytes):
        if session_id in self.active_sessions:
            await self.active_sessions[session_id].send_audio(audio_bytes)

    async def _process_events(self, session_id: str):
        try:
            session = self.active_sessions[session_id]
            websocket = self.websockets[session_id]

            async for event in session:
                event_data = await self._serialize_event(event)
                await websocket.send_text(json.dumps(event_data))
        except Exception as e:
            logger.error(f"Error processing events for session {session_id}: {e}")

    async def _serialize_event(self, event: RealtimeSessionEvent) -> dict[str, Any]:
        base_event: dict[str, Any] = {
            "type": event.type,
        }

        if event.type == "agent_start":
            base_event["agent"] = event.agent.name
        elif event.type == "agent_end":
            base_event["agent"] = event.agent.name
        elif event.type == "handoff":
            base_event["from"] = event.from_agent.name
            base_event["to"] = event.to_agent.name
        elif event.type == "tool_start":
            base_event["tool"] = event.tool.name
        elif event.type == "tool_end":
            base_event["tool"] = event.tool.name
            base_event["output"] = str(event.output)
        elif event.type == "audio":
            base_event["audio"] = base64.b64encode(event.audio.data).decode("utf-8")
        elif event.type == "audio_interrupted":
            pass
        elif event.type == "audio_end":
            pass
        elif event.type == "history_updated":
            base_event["history"] = [item.model_dump(mode="json") for item in event.history]
        elif event.type == "history_added":
            pass
        elif event.type == "guardrail_tripped":
            base_event["guardrail_results"] = [
                {"name": result.guardrail.name} for result in event.guardrail_results
            ]
        elif event.type == "raw_model_event":
            base_event["raw_model_event"] = {
                "type": event.data.type,
            }
        elif event.type == "error":
            base_event["error"] = str(event.error) if hasattr(event, "error") else "Unknown error"
        elif event.type == "input_audio_timeout_triggered":
            pass
        else:
            assert_never(event)

        return base_event


manager = RealtimeWebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    yield


app = FastAPI(lifespan=lifespan)

print("DEBUG: FastAPI app created successfully")

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    print(f"DEBUG: WebSocket connection attempt for session {session_id}")
    await manager.connect(websocket, session_id)
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "audio":
                # Convert int16 array to bytes
                int16_data = message["data"]
                audio_bytes = struct.pack(f"{len(int16_data)}h", *int16_data)
                await manager.send_audio(session_id, audio_bytes)

    except WebSocketDisconnect:
        await manager.disconnect(session_id)


@app.get("/")
async def read_index():
    return FileResponse("static/index.html")

@app.get("/test")
async def test_route():
    return {"message": "API routes working"}

# Mount static files at /static path to avoid conflicts with WebSocket routes
app.mount("/static", StaticFiles(directory="static"), name="static")

print("DEBUG: WebSocket route registered and static files mounted")
print("DEBUG: Server setup complete")


if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8080))
    
    uvicorn.run(app, host=host, port=port)



# docker run -p 8080:8080 -e OPENAI_API_KEY=$OPENAI_API_KEY glyphic-sales-agent