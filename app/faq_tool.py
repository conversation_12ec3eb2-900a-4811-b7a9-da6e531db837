from openai import OpenAI
from agents import function_tool

client = OpenAI()


GLYPHIC_AI_SAAS_PLATFORM_FAQS = """
Glyphic Sales FAQ for New Team Members
This FAQ is based on common questions and discussion points from recent sales calls.

I. General / About Glyphic
Q: What is Glyphic?
A: Glyphic is an AI-native Sales Intelligence Platform. We go beyond basic call recording by using AI to analyze sales conversations (calls, emails), automate CRM data entry, provide deep insights into sales processes, and offer robust coaching capabilities to improve rep performance and efficiency. We focus on the sales cycle from the first booked meeting onwards.
Q: Who founded Glyphic and what's their background?
A: Our co-founders, <PERSON> and <PERSON><PERSON>, were part of the original Google DeepMind team that developed Gemini. They have extensive experience in AI research and LLMs.
Q: How big is Glyphic as a company?
A: We're a growing startup, currently around 14-15 people, with a strong engineering core. We recently expanded our go-to-market team and have around 90 paying customers. We are pre-Series A, looking to raise soon, and are currently just under $1M ARR.
Q: What is Glyphic's Ideal Customer Profile (ICP)?
A: We primarily target B2B SaaS companies and other data-driven organizations with sales teams ranging from 5 reps to 100+ (sweet spot often 20-70 reps). Key indicators are usage of Salesforce or HubSpot, a defined sales process (or a desire to implement one like MEDDIC/SPICED), and challenges with CRM data integrity, rep efficiency, or sales coaching.
Q: Who are Glyphic's main competitors?
A: We most frequently compete with Gong. Other names that come up are Avoma, Attention, Clari Copilot, Chorus (ZoomInfo), Jiminy, and Fathom. We differentiate by being AI-native, offering deeper and more customizable insights, superior CRM automation, and more robust multi-language support and coaching features.
II. Key Features & Benefits
Q: How does Glyphic help with CRM data entry and hygiene?
A: Glyphic automates the population of CRM fields (standard or custom) in Salesforce and HubSpot based on information extracted from calls and emails. This includes sales methodologies (MEDDIC, SPICED, BANT, or custom), competitor mentions, pain points, next steps, and any other custom insights you configure. This saves reps significant admin time and ensures data is accurate and consistent.
Q: What sales methodologies does Glyphic support?
A: We support standard methodologies like MEDDIC, MEDDPICC, SPICED, BANT, Challenger, etc. We can also configure and track against custom sales playbooks and qualification frameworks specific to your organization. This is tracked on both a per-call and aggregated deal level.
Q: How does Glyphic analyze calls and provide summaries?
A: Glyphic records and transcribes calls. Post-call (usually within 2-5 minutes), our AI generates summaries at various levels: a quick 2-3 sentence overview, a more detailed summary aligned with your sales methodology (e.g., SPICED), and extracts key information for custom insights.
Q: What is "AskGlyphic" and how is it used?
A: AskGlyphic is our AI copilot. It allows users to ask natural language questions about specific calls, entire deals (aggregating info from all calls/emails), or globally across all company conversations. For example, "What were the client's main objections on this call?", "What are the risks in this deal?", or "Which competitor was mentioned most frequently last quarter?".
Q: How does Glyphic support sales coaching and performance improvement?
A: Glyphic offers AI-driven scorecards that can be customized with your rubrics and skills (e.g., rapport building, objection handling, value proposition). It scores calls automatically, provides explanations, and suggests improvements. Managers can track rep performance over time, identify trends, and pinpoint areas needing coaching. Reps can also use AskGlyphic for self-coaching (e.g., "How could I have handled that objection better?"). The coaching module is currently in beta and rolling out.
Q: Can Glyphic automate follow-up emails or internal handovers?
A: Yes. AskGlyphic can generate follow-up emails based on call context, which reps can then review and send. It can also create handover documents (e.g., SDR to AE, AE to CS, Sales to Product) based on customizable templates and information from the interactions.
Q: What kind of strategic insights and reporting does Glyphic provide?
A: Glyphic aggregates data from all conversations to provide insights into win/loss reasons, common pain points, competitor mentions, product feature requests, and sentiment analysis. This helps leadership understand market trends, refine sales strategy, guide product development, and inform marketing messaging. This data can be viewed in Glyphic's insight dashboards or pushed to the CRM.
Q: Does Glyphic support multiple languages?
A: Yes, we support over 99 languages, including all major European languages (German, French, Spanish, etc.), Nordic languages (Swedish, Norwegian, Danish), Portuguese, and are improving others like Mandarin. Transcripts remain in the original language, but summaries and AskGlyphic queries/responses can be in English, facilitating cross-language understanding for managers.
Q: How are in-person meetings handled?
A: Reps can record a voice note summary after the meeting and upload it to Glyphic. Alternatively, they can use their laptop to join a Zoom/Teams call with Glyphic and record the audio of the in-person meeting that way.
Q: Is there a mobile app?
A: We don't have a dedicated mobile app currently, but the web platform is mobile-friendly. Reps can also upload voice memos from their phone.
Q: Can users customize the co-pilot's name?
A: Yes, the Glyphic co-pilot that joins calls can be renamed (e.g., "YourCompanyName Notetaker").
Q: How does Glyphic help with new rep onboarding?
A: New reps can use AskGlyphic to quickly get up to speed on products, competitor positioning, and successful sales techniques by querying past calls. They can also review top-performing reps' calls (identified through scorecards or filtering) and access automatically generated sales playbooks.
III. Integrations (CRM, Dialers, etc.)
Q: Which CRMs do you integrate with?
A: Our deepest, bi-directional integrations are with Salesforce and HubSpot. We can sync notes, activities, and populate standard/custom fields. We offer basic note-syncing for Pipedrive. We currently do not integrate with Zoho or most other smaller/custom CRMs.
Q: What data is synced to the CRM?
A: Call summaries, links to recordings, populated sales methodology fields (e.g., MEDDIC), custom insights, and tasks. For Salesforce and HubSpot, this can be mapped to specific fields on contact, company, and deal/opportunity objects. The sync is often bi-directional for things like deal stages.
Q: Do you integrate with sales engagement platforms or dialers?
A: Yes. We can process calls made through Outreach (including its dialer), Aircall, and the HubSpot dialer. If other dialers log call recordings into the CRM (e.g., Salesforce, HubSpot), we can often pull and analyze them from there. We are continuously expanding these.
Q: Can Glyphic read and analyze emails?
A: Yes, if emails are logged in an integrated CRM (like HubSpot or Salesforce via Einstein Activity Capture if configured correctly), Glyphic can ingest and analyze them as part of the overall deal context. We can also connect directly to Outlook/Gmail.
Q: Do you integrate with Slack or Microsoft Teams?
A: We have a robust Slack integration. Deal-specific updates, call summaries, and AskGlyphic queries can happen within Slack deal rooms. Teams integration is more limited; summaries can be sent, but interactive features are better in Slack.
Q: Is there an API for custom integrations?
A: Yes, we have an API. For example, Jointflows wanted to access transcripts via API. We are also working on broader API access and Zapier integration (e.g., Lumar).
IV. Technical & Security
Q: Where is customer data hosted?
A: For our European clients, data is hosted on AWS in Ireland, ensuring GDPR compliance.
Q: What security certifications does Glyphic have?
A: We are SOC 2 Type 2 compliant and GDPR compliant. We can provide our Vanta trust package.
Q: Does Glyphic use my company's call data to train its general AI models?
A: No. Your data is used to fine-tune the AI for your specific Glyphic instance to improve its understanding of your products, jargon, and sales process. It is not used to train our global models or shared with other customers.
Q: What LLM (Large Language Model) does Glyphic use?
A: We use a combination of best-in-class LLMs, including models from Anthropic (Claude), AssemblyAI, OpenAI, and Google (Gemini). We route specific tasks to the model best suited for them (e.g., one for transcription, another for summarization) and continuously evaluate new models to ensure top performance. This is augmented with our own proprietary training for GTM context.
Q: How does Glyphic handle call recording consent (e.g., GDPR, two-party consent)?
A: The Glyphic co-pilot announces its presence and recording purpose in the chat (and can optionally do so via audio). For Zoom, it respects Zoom's native consent mechanisms. Users are responsible for ensuring they comply with local regulations regarding consent before initiating recording. We can send pre-meeting emails about recording.
V. Pricing & Process
Q: What is Glyphic's pricing model?
A: We price per user, per month, typically on an annual contract. There are two main license types:
Recording Seat: For users whose calls need to be recorded and analyzed (e.g., AEs, SDRs). Standard pricing is around $140/user/month, but this is subject to volume discounts.
Viewer Seat: For users who need to access data, insights, and reports but don't record calls (e.g., managers, RevOps, product, marketing). This is typically half the price of a recording seat (around $70/user/month).
Q: Are there any startup packages or discounts?
A: Yes, we have a startup package, e.g., for up to 5 users at $3,000 per annum (effectively $50/user/month). We are open to negotiating pricing based on volume, contract length (e.g., multi-year), and strategic partnerships. We've negotiated down to $50/seat for some clients like Recruiterflow for a specific number of seats.
Q: Do you offer a free trial or Proof of Concept (POC)?
A: Yes, we typically offer a 2-week free POC. This can sometimes be extended. For POCs, we often sign a contract with a break-clause, meaning no payment if you decide not to continue.
Q: How long does implementation take?
A: Basic setup (connecting calendars, CRM) can be done in minutes. Configuring custom insights, sales methodologies, and specific workflows is a collaborative process and depends on complexity but is generally quick (e.g., a week or two for initial focused rollout).
Q: What does onboarding and support look like?
A: We provide "white glove" onboarding, working with you to set up the platform according to your needs. Post-onboarding, support is typically managed via a shared Slack channel with our CSMs and engineers for quick resolution. We also have email support and are building out more self-serve product support pages.
Q: Can Glyphic migrate historical call data from other platforms (e.g., Gong, Chorus, Fireflies)?
A: Yes. The process usually involves the customer exporting their data (transcripts, sometimes video/audio files) from the old platform, and Glyphic assists with ingesting and processing it. We have a pre-built connector for Gong to streamline this.
Q: What if we're currently in a contract with another provider like Gong or Jiminy?
A: We are open to discussing contract buyouts, especially if you're committing to a multi-year agreement with Glyphic, to make the transition smoother.
VI. Competitive Differentiation
Q: How is Glyphic different from Gong?
A: While Gong is a strong call recording tool, Glyphic is AI-native and offers:
Deeper AI Insights: More sophisticated analysis beyond keyword tracking, understanding context, sentiment, and complex sales methodologies.
Superior CRM Automation: More robust and customizable data syncing to Salesforce/HubSpot fields.
Customization: Highly flexible in adapting to specific sales frameworks, rubrics, and insight tracking.
LLM Agility: We use multiple best-in-class LLMs and can adapt quickly, whereas Gong's AI is more of an add-on to a legacy system.
Faster Processing: Call analysis is typically much faster.
Q: How do you compare to simpler tools like Fathom, Fireflies, or Chorus?
A: Those tools are primarily call recorders and transcribers with basic summarization. Glyphic provides a full sales intelligence suite, including deal-level aggregation, advanced AI-driven insights, customizable coaching scorecards, CRM automation, and proactive features like call prep sheets.
Q: What about tools like Avoma or Attention?
A: We believe our AI capabilities are more advanced, particularly in understanding sales context, providing accurate and actionable insights, and offering deeper customization for sales processes and CRM integration. Our focus on strategic data analysis (win/loss, competitor trends tied to revenue) and robust coaching frameworks are key differentiators. We also emphasize the quality and reliability of the data extracted.
"""




SYSTEM = f"""
You are a strict FAQ answerer.
Use only the information in FAQ_KB below.
If the answer is not in FAQ_KB, reply: "I don’t know, please check with the team."
Do not invent or rephrase answers.

FAQ_KB = \n{GLYPHIC_AI_SAAS_PLATFORM_FAQS}
"""

@function_tool
def faq_lookup_tool(query: str) -> str:
    """Lookup frequently asked questions about Glyphic."""
    resp = client.chat.completions.create(
        model="gpt-5-nano-2025-08-07",   # cheap, fast model is enough
        messages=[
            {"role":"system","content":SYSTEM},
            {"role":"user","content":query}
        ]
    )
    return resp.choices[0].message.content

# print(faq_lookup_tool("Who are Glyphic's main competitors?"))
