"""
Email service module for sending transactional emails via SendGrid.
"""

import os
import logging
from typing import Optional
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Email, To, Content

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails via SendGrid."""
    
    def __init__(self):
        self.api_key = os.getenv('SENDGRID_API_KEY')
        self.from_email = os.getenv('FROM_EMAIL', '<EMAIL>')
        self.from_name = os.getenv('FROM_NAME', 'Glyphic AI Team')
        
        if self.api_key:
            self.sg = SendGridAPIClient(api_key=self.api_key)
            self.enabled = True
            logger.info("SendGrid email service initialized")
        else:
            self.sg = None
            self.enabled = False
            logger.warning("SendGrid API key not found. Email service disabled. Set SENDGRID_API_KEY environment variable to enable.")
    
    def send_email(self, to_email: str, subject: str, html_content: str, text_content: Optional[str] = None) -> dict:
        """
        Send an email via SendGrid.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML content of the email
            text_content: Plain text content (optional, will be generated from HTML if not provided)
        
        Returns:
            dict: Result with success status and message
        """
        if not self.enabled:
            # Fallback to console logging when SendGrid is not configured
            logger.info("=== EMAIL SERVICE (DEMO MODE) ===")
            logger.info(f"To: {to_email}")
            logger.info(f"From: {self.from_name} <{self.from_email}>")
            logger.info(f"Subject: {subject}")
            logger.info("Content:")
            logger.info(html_content)
            logger.info("=== END EMAIL ===")
            
            return {
                "success": True,
                "message": f"Email logged to console (SendGrid not configured). Would send to {to_email}",
                "demo_mode": True
            }
        
        try:
            # Create the email
            from_email_obj = Email(self.from_email, self.from_name)
            to_email_obj = To(to_email)
            
            # Use text content if provided, otherwise let SendGrid handle it
            if text_content:
                content = Content("text/plain", text_content)
                mail = Mail(from_email_obj, to_email_obj, subject, content)
                mail.add_content(Content("text/html", html_content))
            else:
                content = Content("text/html", html_content)
                mail = Mail(from_email_obj, to_email_obj, subject, content)
            
            # Send the email
            response = self.sg.send(mail)
            
            if response.status_code in [200, 201, 202]:
                logger.info(f"Email sent successfully to {to_email}")
                return {
                    "success": True,
                    "message": f"Email sent successfully to {to_email}",
                    "status_code": response.status_code
                }
            else:
                logger.error(f"Failed to send email. Status code: {response.status_code}")
                return {
                    "success": False,
                    "message": f"Failed to send email. Status code: {response.status_code}",
                    "status_code": response.status_code
                }
                
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return {
                "success": False,
                "message": f"Error sending email: {str(e)}"
            }
    
    def send_contract_completion_email(self, email: str, company_name: str, person_name: str, 
                                     number_of_recording_seats: int, contract_start_date: str, 
                                     contract_end_date: str, contract_price: int) -> dict:
        """
        Send a contract completion email with professional HTML formatting.
        
        Returns:
            dict: Result with success status and message
        """
        subject = f"Welcome to Glyphic AI - Contract Signed for {company_name}"
        
        # HTML email content with professional styling
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{subject}</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; }}
                .footer {{ background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666; }}
                .contract-details {{ background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }}
                .next-steps {{ background: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0; }}
                .step {{ margin: 10px 0; }}
                .highlight {{ color: #667eea; font-weight: bold; }}
                ul {{ padding-left: 20px; }}
                li {{ margin: 8px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to Glyphic AI!</h1>
                    <p>Your contract has been successfully signed</p>
                </div>
                
                <div class="content">
                    <p>Dear <strong>{person_name}</strong>,</p>
                    
                    <p>Congratulations! Your contract with Glyphic AI has been successfully signed. We're excited to help you transform your sales process with AI-powered insights.</p>
                    
                    <div class="contract-details">
                        <h3>📋 Contract Details</h3>
                        <ul>
                            <li><strong>Company:</strong> {company_name}</li>
                            <li><strong>Contact Person:</strong> {person_name}</li>
                            <li><strong>Recording Seats:</strong> {number_of_recording_seats}</li>
                            <li><strong>Contract Period:</strong> {contract_start_date} to {contract_end_date}</li>
                            <li><strong>Total Price:</strong> <span class="highlight">${contract_price:,}</span></li>
                        </ul>
                    </div>
                    
                    <div class="next-steps">
                        <h3>🚀 Next Steps</h3>
                        <ol>
                            <li><strong>Account Provisioning:</strong> Your Glyphic AI account is being set up and will be ready within 24 hours</li>
                            <li><strong>Login Credentials:</strong> You'll receive your login details via email shortly</li>
                            <li><strong>Onboarding:</strong> Our Customer Success team will reach out to schedule your onboarding session</li>
                            <li><strong>Integration Setup:</strong> We'll help you connect your CRM, calendar, and other tools</li>
                            <li><strong>Training:</strong> Comprehensive training sessions will be scheduled for your team</li>
                        </ol>
                    </div>
                    
                    <h3>✨ What to Expect</h3>
                    <ul>
                        <li>White-glove onboarding experience</li>
                        <li>Dedicated Customer Success Manager</li>
                        <li>24/7 support via our shared Slack channel</li>
                        <li>Access to all Glyphic AI features from day one</li>
                    </ul>
                    
                    <p>If you have any questions or need immediate assistance, please don't hesitate to reach out to our team.</p>
                    
                    <p><strong>Welcome to the Glyphic AI family!</strong></p>
                    
                    <p>Best regards,<br>
                    <strong>The Glyphic AI Team</strong></p>
                </div>
                
                <div class="footer">
                    <p>This is an automated message. For support, contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>Glyphic AI | Transforming Sales with AI-Powered Intelligence</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version for email clients that don't support HTML
        text_content = f"""
Dear {person_name},

Congratulations! Your contract with Glyphic AI has been successfully signed.

CONTRACT DETAILS:
• Company: {company_name}
• Contact Person: {person_name}
• Recording Seats: {number_of_recording_seats}
• Contract Period: {contract_start_date} to {contract_end_date}
• Total Price: ${contract_price:,}

NEXT STEPS:
1. Account Provisioning: Your Glyphic AI account is being set up and will be ready within 24 hours
2. Login Credentials: You'll receive your login details via email shortly
3. Onboarding: Our Customer Success team will reach out to schedule your onboarding session
4. Integration Setup: We'll help you connect your CRM, calendar, and other tools
5. Training: Comprehensive training sessions will be scheduled for your team

WHAT TO EXPECT:
• White-glove onboarding experience
• Dedicated Customer Success Manager
• 24/7 support via our shared Slack channel
• Access to all Glyphic AI features from day one

If you have any questions or need immediate assistance, please don't hesitate to reach out to our team.

Welcome to the Glyphic AI family!

Best regards,
The Glyphic AI Team

---
This is an automated message. For support, contact <NAME_EMAIL>
        """
        
        return self.send_email(email, subject, html_content, text_content)

# Global email service instance
email_service = EmailService()
