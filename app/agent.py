from agents import function_tool
from agents.extensions.handoff_prompt import RECOMMENDED_PROMPT_PREFIX
from agents.realtime import RealtimeAgent, realtime_handoff

from faq_tool import faq_lookup_tool
from email_service import email_service

"""
When running the UI example locally, you can edit this file to change the setup. THe server
will use the agent returned from get_starting_agent() as the starting agent."""

### TOOLS
GLYPHIC_AI_SAAS_PLATFORM_DESCRIPTION = """
Glyphic is an AI-powered sales and revenue intelligence platform that acts as a co-pilot for go-to-market teams. It captures, analyzes, and enriches customer interactions (calls, emails, meetings) to improve sales efficiency, deal execution, and coaching.

Core Capabilities:

Conversation Capture & Transcription: Multi-platform, multilingual, CRM-integrated.

AI Summaries & Notes: Auto-generated, customizable (SPICED, MEDDIC, BANT, etc.).

CRM Automation: Real-time syncing of insights (qualification, competitors, sentiment, requests).

Conversational Search ("Ask Glyphic"): Natural language queries across all interactions.

Strategic Analytics: Trends, win/loss drivers, competitor mentions, feature requests.

Coaching & Enablement: Call scoring, improvement areas, playbooks from best practices.

Deal & Pipeline Support: Risk identification, next steps, methodology tracking.

Pre-Call Prep & Follow-ups: Automated prep sheets, follow-up emails, and handovers.

Customization & Memory: Tailors insights, workflows, and prompts to team preferences.

Technical Strengths:

AI-Native: Built with cutting-edge LLMs (Anthropic Claude, OpenAI, proprietary).

Integrations: Deep Salesforce & HubSpot support; email, calendar, Slack; expanding to more.

Security & Compliance: SOC2 Type 2, GDPR compliant, secure regional data hosting.

Mission (Inferred): Empower GTM teams with actionable intelligence from customer interactions.
Vision (Inferred): Be the leading AI co-pilot for GTM teams by unlocking conversational data.
"""



# @function_tool(
#     name_override="faq_lookup_tool", description_override="Lookup frequently asked questions."
# )
# async def faq_lookup_tool(question: str) -> str:
#     if "bag" in question or "baggage" in question:
#         return (
#             "You are allowed to bring one bag on the plane. "
#             "It must be under 50 pounds and 22 inches x 14 inches x 9 inches."
#         )
#     elif "seats" in question or "plane" in question:
#         return (
#             "There are 120 seats on the plane. "
#             "There are 22 business class seats and 98 economy seats. "
#             "Exit rows are rows 4 and 16. "
#             "Rows 5-8 are Economy Plus, with extra legroom. "
#         )
#     elif "wifi" in question:
#         return "We have free wifi on the plane, join Airline-Wifi"
#     return "I'm sorry, I don't know the answer to that question."


@function_tool
async def sign_contract(confirmation_number: str, new_seat: str) -> str:
    """
    Update the seat for a given confirmation number.

    Args:
        confirmation_number: The confirmation number for the flight.
        new_seat: The new seat to update to.
    """
    return f"Updated seat to {new_seat} for confirmation number {confirmation_number}"


@function_tool
def get_weather(city: str) -> str:
    """Get the weather in a city."""
    return f"The weather in {city} is sunny."


@function_tool
def send_email(email: str, subject: str = "Welcome to Glyphic AI", content: str = "") -> str:
    """Sends an email to the user with optional subject and content."""
    result = email_service.send_email(email, subject, content)
    if result["success"]:
        return f"Email sent successfully to {email} with subject '{subject}'. {result['message']}"
    else:
        return f"Failed to send email to {email}. Error: {result['message']}"


@function_tool
def send_contract_completion_email(email: str, company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int) -> str:
    """Sends a contract completion email with all contract details and next steps."""
    return _send_contract_completion_email_internal(email, company_name, person_name, number_of_recording_seats, contract_start_date, contract_end_date, contract_price)


@function_tool
def crm_close_deal(deal_name: str) -> str:
    """Closes the deal in the CRM."""
    print(f"Closing deal {deal_name} in CRM.")
    return "Deal closed in CRM."

def _send_contract_completion_email_internal(email: str, company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int) -> str:
    """Internal function to send contract completion email using the email service."""
    result = email_service.send_contract_completion_email(
        email=email,
        company_name=company_name,
        person_name=person_name,
        number_of_recording_seats=number_of_recording_seats,
        contract_start_date=contract_start_date,
        contract_end_date=contract_end_date,
        contract_price=contract_price
    )

    if result["success"]:
        return f"Contract completion email sent successfully to {email} for {company_name}."
    else:
        return f"Failed to send contract completion email to {email}. Error: {result['message']}"


@function_tool
def alguna_tool(company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int, email: str = "") -> str:
    """Gets the prospect to sign the contract and sends a completion email."""
    print(f"Getting prospect to sign the contract for {company_name} with {person_name} for {number_of_recording_seats} recording seats starting on {contract_start_date} and ending on {contract_end_date} for {contract_price}.")

    # Simulate contract signing process
    contract_signed = True

    if contract_signed:
        # If email is provided, send the contract completion email
        if email:
            email_result = _send_contract_completion_email_internal(
                email=email,
                company_name=company_name,
                person_name=person_name,
                number_of_recording_seats=number_of_recording_seats,
                contract_start_date=contract_start_date,
                contract_end_date=contract_end_date,
                contract_price=contract_price
            )
            return f"Contract successfully signed for {company_name}! {email_result} Next steps: Account provisioning will begin immediately, and you'll receive login credentials within 24 hours."
        else:
            return f"Contract successfully signed for {company_name} with {person_name} for {number_of_recording_seats} recording seats starting on {contract_start_date} and ending on {contract_end_date} for ${contract_price:,}. Please provide your email address to receive the welcome email with next steps."
    else:
        return f"There was an issue with the contract signing process. Please try again or contact support."


@function_tool
def provision_account(company_name: str) -> str:
    """Provision account for the company."""
    print(f"Provisioning account for {company_name}")
    return f"Account provisioned for {company_name}."



faq_agent = RealtimeAgent(
    name="FAQ Agent",
    handoff_description="A helpful agent that can answer questions about Glyphic.",
    instructions=f"""{RECOMMENDED_PROMPT_PREFIX}
    You are an FAQ agent. If you are speaking to a prospect, you probably were transferred to from the sales agent.
    Use the following routine to support the prospect.
    # Routine
    1. Identify the last question asked by the prospect.
    2. Use the faq lookup tool to answer the question. Do not rely on your own knowledge.
    3. If you cannot answer the question, transfer back to the sales agent.""",
    tools=[faq_lookup_tool],
)

alguna_agent = RealtimeAgent(
    name="Alguna contract signing agent",
    handoff_description="A helpful agent that can create and sign a contract.",
    instructions=f"""{RECOMMENDED_PROMPT_PREFIX}
    You are a contract signing agent. If you are speaking to a prospect, you probably were transferred to from the sales agent.

    Follow this routine to complete the contract signing process:

    # CONTRACT SIGNING ROUTINE:
    1. **COLLECT CONTRACT INFORMATION**: Gather all necessary details for the contract:
       - Company name
       - Contact person's full name
       - Number of recording seats needed
       - Contract start date (suggest starting next Monday if not specified)
       - Contract end date (typically 12 months from start date)
       - Contract price (calculate based on $140/seat/month * seats * months, with volume discounts for 10+ seats)
       - Do not just give away the price. Tease it and ask if they are interested in a discount.
       - If they are interested in a discount, ask for the number of seats and the contract length and calculate the price.
       - Never take down the price per seat lower than 120. 
       - Email address for contract completion notification

    2. **CONFIRM DETAILS**: Review all information with the prospect to ensure accuracy

    3. **EXECUTE CONTRACT**: Use the alguna_tool to process the contract with all collected information

    4. **COMPLETION**: Once the contract is signed:
       - Confirm the contract completion email has been sent
       - Inform them about next steps (account provisioning, login credentials, onboarding)
       - Let them know the Customer Success team will be in touch within 24 hours
       - Transfer back to the sales agent for final wrap-up

    # IMPORTANT NOTES:
    - Always collect the email address - it's required for the completion process
    - Be thorough in collecting all information before proceeding with contract signing
    - If the customer asks questions not related to contract signing, transfer back to the sales agent
    - Maintain a professional and helpful tone throughout the process
    """,
    tools=[alguna_tool],
)




sales_agent = RealtimeAgent(
    name="Sales Agent",
    handoff_description=
    "A sales agent whose job is to sell Glyphic AIs SaaS platform to people.",
    instructions=
    (f"{RECOMMENDED_PROMPT_PREFIX} "
     "You are a helpful AI account executive for Glyphic AI who works in ENGLISH. Follow this conversation flow:\n\n"
     
     "# CONVERSATION FLOW:\n"
     "1. **WARM OPENING**: Start with friendly small talk to build rapport. Ask about their day, company, or industry trends.\n\n"
     
     "2. **DISCOVERY PHASE**: Focus on discovery questions about their current challenges:\n"
     "   - Ask how much time their sales team currently spends on manual tasks\n"
     "   - Explore how these manual processes impede their sales team's ability to focus on selling\n"
     "   - Understand their current pain points with data entry, note-taking, CRM updates, etc.\n"
     "   - Listen actively and ask follow-up questions to uncover specific challenges\n\n"
     
     "3. **PERSONALIZED PITCH**: Based on their responses, deliver a tailored pitch showing how Glyphic AI solves their specific problems:\n"
     f"   {GLYPHIC_AI_SAAS_PLATFORM_DESCRIPTION}\n"
     "   - Connect their pain points directly to Glyphic's solutions\n"
     "   - Emphasize time savings, efficiency gains, and revenue impact\n"
     "   - Use specific examples relevant to their situation\n\n"
     
     "4. **CLOSING**: Ask if they're ready to take the next step and supercharge their revenue with Glyphic AI. Remember that you can use contract agent to sign the contract.\n\n"
     
     "5. **CONTRACT HANDOFF**: If they show interest in moving forward, transfer them to the alguna agent for contracting. "
     "Tell them once the contract is signed, you will provision the account and send them the login details. \n\n"
     
     "6. **WARM CLOSE**: End conversations warmly and let them know the Customer Success team will get in touch with them shortly if there are any other questions.\n\n"
     
     "# ADDITIONAL GUIDELINES:\n"
     "- Always be consultative, not pushy\n"
     "- Ask permission before transitioning between phases\n"
     "- If they have specific questions about Glyphic, transfer to the FAQ agent\n"
     "- Use your tools to send follow-up emails and close deals in the CRM when appropriate\n"
     "- Keep the conversation natural and conversational\n"
     "REMEMBER: Always communicate in ENGLISH"
     ),
    handoffs=[faq_agent, realtime_handoff(alguna_agent)],
    tools=[send_email, send_contract_completion_email, crm_close_deal, provision_account],
)

faq_agent.handoffs.append(sales_agent)
alguna_agent.handoffs.append(sales_agent)


def get_starting_agent() -> RealtimeAgent:
    return sales_agent
